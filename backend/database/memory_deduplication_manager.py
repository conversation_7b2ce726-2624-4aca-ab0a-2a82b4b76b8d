"""
内存去重管理器
实现基于内存的全字段匹配去重方案，优化算法结果表的数据写入流程
"""

import json
import logging
import hashlib
from typing import Dict, List, Set, Any, Optional, Tuple
from datetime import datetime
from .duckdb_manager import DuckDBManager

logger = logging.getLogger(__name__)

class MemoryDeduplicationManager:
    """内存去重管理器 - 全字段匹配去重"""
    
    def __init__(self, db_manager: Optional[DuckDBManager] = None):
        """初始化内存去重管理器"""
        self.db_manager = db_manager or DuckDBManager()
        
        # 目标表配置
        self.target_tables = {
            'algorithm_results': {
                'primary_key': 'id',
                'exclude_fields': ['id', 'created_at', 'updated_at']  # 排除自动生成的字段
            },
            'wash_trading_results': {
                'primary_key': 'id', 
                'exclude_fields': ['id', 'created_at']
            },
            'same_account_wash_trading': {
                'primary_key': 'id',
                'exclude_fields': ['id', 'created_at']
            },
            'cross_account_wash_trading': {
                'primary_key': 'id',
                'exclude_fields': ['id', 'created_at']
            }
        }
        
        # 内存缓存：存储每张表的记录哈希值
        self.memory_cache: Dict[str, Set[str]] = {}
        
        # 统计信息
        self.stats = {
            'loaded_records': {},
            'duplicate_filtered': {},
            'new_records_added': {}
        }
        
        logger.info("内存去重管理器初始化完成")
    
    def _normalize_record_for_comparison(self, record: Dict[str, Any], table_name: str) -> str:
        """
        将记录标准化为可比较的哈希值
        排除自动生成的字段，只比较业务字段
        """
        try:
            table_config = self.target_tables.get(table_name, {})
            exclude_fields = table_config.get('exclude_fields', [])
            
            # 创建用于比较的字段字典
            comparison_dict = {}
            for key, value in record.items():
                if key not in exclude_fields:
                    # 标准化值
                    if value is None:
                        comparison_dict[key] = None
                    elif isinstance(value, (dict, list)):
                        # JSON对象需要排序后序列化确保一致性
                        comparison_dict[key] = json.dumps(value, sort_keys=True, ensure_ascii=False)
                    elif isinstance(value, datetime):
                        # 时间对象转换为ISO格式字符串
                        comparison_dict[key] = value.isoformat()
                    elif isinstance(value, (int, float)):
                        # 数值类型直接使用
                        comparison_dict[key] = value
                    else:
                        # 字符串类型
                        comparison_dict[key] = str(value)
            
            # 生成哈希值
            record_str = json.dumps(comparison_dict, sort_keys=True, ensure_ascii=False)
            record_hash = hashlib.md5(record_str.encode('utf-8')).hexdigest()
            
            return record_hash
            
        except Exception as e:
            logger.error(f"标准化记录失败 {table_name}: {e}")
            # 降级处理：使用整个记录的字符串表示
            return hashlib.md5(str(record).encode('utf-8')).hexdigest()
    
    def load_table_to_memory(self, table_name: str) -> bool:
        """
        加载指定表的现有数据到内存缓存
        返回是否加载成功
        """
        try:
            if table_name not in self.target_tables:
                logger.warning(f"表 {table_name} 不在目标表列表中")
                return False
            
            logger.info(f"开始加载表 {table_name} 到内存...")
            
            # 查询表中所有数据
            sql = f"SELECT * FROM {table_name}"
            
            with self.db_manager.get_connection() as conn:
                # 检查表是否存在
                table_exists = conn.execute(f"""
                    SELECT COUNT(*) as table_count 
                    FROM information_schema.tables 
                    WHERE table_name = '{table_name}'
                """).fetchone()[0]
                
                if table_exists == 0:
                    logger.info(f"表 {table_name} 不存在，初始化空缓存")
                    self.memory_cache[table_name] = set()
                    self.stats['loaded_records'][table_name] = 0
                    return True
                
                # 获取表数据
                results = conn.execute(sql).fetchall()
                columns = [desc[0] for desc in conn.description]
                
                # 转换为字典格式并生成哈希
                record_hashes = set()
                for row in results:
                    record_dict = dict(zip(columns, row))
                    record_hash = self._normalize_record_for_comparison(record_dict, table_name)
                    record_hashes.add(record_hash)
                
                # 更新内存缓存
                self.memory_cache[table_name] = record_hashes
                self.stats['loaded_records'][table_name] = len(record_hashes)
                
                logger.info(f"表 {table_name} 加载完成: {len(record_hashes)} 条记录")
                return True
                
        except Exception as e:
            logger.error(f"加载表 {table_name} 到内存失败: {e}")
            # 初始化空缓存以避免后续错误
            self.memory_cache[table_name] = set()
            self.stats['loaded_records'][table_name] = 0
            return False

    def deduplicate_records(self, table_name: str, new_records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对新记录进行全字段匹配去重
        返回去重后的记录列表
        """
        try:
            if not new_records:
                logger.info(f"表 {table_name} 没有新记录需要去重")
                return []

            # 确保内存缓存已加载
            if table_name not in self.memory_cache:
                if not self.load_table_to_memory(table_name):
                    logger.error(f"无法加载表 {table_name} 到内存，跳过去重")
                    return new_records

            logger.info(f"开始对表 {table_name} 的 {len(new_records)} 条新记录进行去重...")

            unique_records = []
            duplicate_count = 0

            for record in new_records:
                # 生成记录哈希
                record_hash = self._normalize_record_for_comparison(record, table_name)

                # 检查是否重复
                if record_hash not in self.memory_cache[table_name]:
                    unique_records.append(record)
                else:
                    duplicate_count += 1
                    logger.debug(f"发现重复记录，已过滤: {record_hash[:8]}...")

            # 更新统计信息
            self.stats['duplicate_filtered'][table_name] = duplicate_count
            self.stats['new_records_added'][table_name] = len(unique_records)

            logger.info(f"表 {table_name} 去重完成: 原始{len(new_records)}条 → 去重后{len(unique_records)}条 (过滤{duplicate_count}条重复)")

            return unique_records

        except Exception as e:
            logger.error(f"去重处理失败 {table_name}: {e}")
            # 降级处理：返回原始记录
            return new_records

    def update_memory_cache(self, table_name: str, new_records: List[Dict[str, Any]]) -> bool:
        """
        更新内存缓存，添加新记录的哈希值
        """
        try:
            if not new_records:
                return True

            # 确保缓存存在
            if table_name not in self.memory_cache:
                self.memory_cache[table_name] = set()

            # 添加新记录的哈希到缓存
            added_count = 0
            for record in new_records:
                record_hash = self._normalize_record_for_comparison(record, table_name)
                if record_hash not in self.memory_cache[table_name]:
                    self.memory_cache[table_name].add(record_hash)
                    added_count += 1

            logger.info(f"内存缓存更新完成 {table_name}: 新增 {added_count} 条记录哈希")
            return True

        except Exception as e:
            logger.error(f"更新内存缓存失败 {table_name}: {e}")
            return False

    def process_with_deduplication(self, table_name: str, new_records: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Dict[str, int]]:
        """
        完整的去重处理流程：加载 → 去重 → 更新缓存
        返回: (去重后的记录列表, 统计信息)
        """
        try:
            logger.info(f"开始完整去重流程 {table_name}: {len(new_records)} 条新记录")

            # 1. 加载现有数据到内存
            if not self.load_table_to_memory(table_name):
                logger.warning(f"加载表 {table_name} 失败，但继续处理")

            # 2. 全字段比较去重
            unique_records = self.deduplicate_records(table_name, new_records)

            # 3. 更新内存缓存
            if unique_records:
                self.update_memory_cache(table_name, unique_records)

            # 4. 返回结果和统计信息
            stats = {
                'original_count': len(new_records),
                'unique_count': len(unique_records),
                'duplicate_count': len(new_records) - len(unique_records),
                'cache_size': len(self.memory_cache.get(table_name, set()))
            }

            logger.info(f"完整去重流程完成 {table_name}: {stats}")
            return unique_records, stats

        except Exception as e:
            logger.error(f"完整去重流程失败 {table_name}: {e}")
            # 降级处理：返回原始记录
            return new_records, {'error': str(e)}

    def get_deduplication_stats(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        return {
            'memory_cache_sizes': {table: len(hashes) for table, hashes in self.memory_cache.items()},
            'processing_stats': self.stats.copy(),
            'target_tables': list(self.target_tables.keys())
        }

    def clear_cache(self, table_name: Optional[str] = None):
        """清空内存缓存"""
        if table_name:
            if table_name in self.memory_cache:
                del self.memory_cache[table_name]
                logger.info(f"已清空表 {table_name} 的内存缓存")
        else:
            self.memory_cache.clear()
            logger.info("已清空所有内存缓存")

    def is_table_supported(self, table_name: str) -> bool:
        """检查表是否支持去重"""
        return table_name in self.target_tables
