#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试去重问题
分析为什么相同的测试数据没有被识别为重复
"""

import sys
import os
import json
import hashlib
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_hash_generation():
    """调试哈希生成过程"""
    logger.info("=== 调试哈希生成过程 ===")
    
    # 模拟相同的业务数据，但不同的ID和时间
    record1 = {
        'id': 1,
        'wash_trading_id': 100,  # 不同的wash_trading_id
        'user_a_id': 'test_user_1',
        'user_b_id': 'test_user_2', 
        'contract_name': 'BTCUSDT',
        'created_at': datetime.now()
    }
    
    record2 = {
        'id': 2,
        'wash_trading_id': 101,  # 不同的wash_trading_id
        'user_a_id': 'test_user_1',  # 相同的业务数据
        'user_b_id': 'test_user_2',  # 相同的业务数据
        'contract_name': 'BTCUSDT',  # 相同的业务数据
        'created_at': datetime.now()
    }
    
    # 去重配置（修复后）
    exclude_fields = ['id', 'created_at', 'wash_trading_id']
    
    def normalize_record(record, table_name):
        """模拟去重管理器的标准化逻辑"""
        comparison_dict = {}
        for key, value in record.items():
            if key not in exclude_fields:
                if value is None:
                    comparison_dict[key] = None
                elif isinstance(value, (dict, list)):
                    comparison_dict[key] = json.dumps(value, sort_keys=True, ensure_ascii=False)
                elif isinstance(value, datetime):
                    comparison_dict[key] = value.isoformat()
                elif isinstance(value, (int, float)):
                    comparison_dict[key] = value
                else:
                    comparison_dict[key] = str(value)
        
        record_str = json.dumps(comparison_dict, sort_keys=True, ensure_ascii=False)
        record_hash = hashlib.md5(record_str.encode('utf-8')).hexdigest()
        
        logger.info(f"记录标准化:")
        logger.info(f"  原始记录: {record}")
        logger.info(f"  比较字典: {comparison_dict}")
        logger.info(f"  序列化字符串: {record_str}")
        logger.info(f"  哈希值: {record_hash}")
        
        return record_hash
    
    # 生成哈希
    hash1 = normalize_record(record1, 'cross_account_wash_trading')
    hash2 = normalize_record(record2, 'cross_account_wash_trading')
    
    logger.info(f"\n比较结果:")
    logger.info(f"哈希1: {hash1}")
    logger.info(f"哈希2: {hash2}")
    logger.info(f"是否相同: {hash1 == hash2}")
    
    if hash1 != hash2:
        logger.error("❌ 问题确认：相同业务数据生成了不同哈希值")
        logger.error("原因：wash_trading_id 字段不同导致哈希不同")
        return False
    else:
        logger.info("✅ 哈希生成正确")
        return True

def debug_wash_trading_id_issue():
    """调试 wash_trading_id 问题"""
    logger.info("=== 调试 wash_trading_id 问题 ===")
    
    logger.info("问题分析:")
    logger.info("1. cross_account_wash_trading 表包含 wash_trading_id 字段")
    logger.info("2. wash_trading_id 是从 wash_trading_results 表的新记录ID生成的")
    logger.info("3. 每次运行算法时，wash_trading_results 都会生成新的ID")
    logger.info("4. 因此即使业务数据相同，wash_trading_id 也不同")
    logger.info("5. 导致 cross_account_wash_trading 记录的哈希值不同")
    
    logger.info("\n解决方案选项:")
    logger.info("选项1: 将 wash_trading_id 也加入排除字段")
    logger.info("选项2: 基于业务字段生成稳定的 wash_trading_id")
    logger.info("选项3: 修改去重策略，只比较核心业务字段")
    
    return True

def debug_incremental_vs_normal():
    """调试增量模式vs普通模式的差异"""
    logger.info("=== 调试增量模式vs普通模式的差异 ===")
    
    logger.info("增量模式问题:")
    logger.info("1. 增量处理器有自己的对敲检测逻辑")
    logger.info("2. 增量模式可能不使用 AlgorithmStorageManager 存储")
    logger.info("3. 导致增量模式下的数据没有经过去重处理")
    
    logger.info("\n需要检查的位置:")
    logger.info("- incremental_processor.py 中的对敲检测存储逻辑")
    logger.info("- 是否调用了 AlgorithmStorageManager.store_algorithm_result")
    logger.info("- 增量模式的数据流向")
    
    return True

def suggest_solutions():
    """建议解决方案"""
    logger.info("=== 建议解决方案 ===")
    
    logger.info("问题1解决方案 - 增量算法去重:")
    logger.info("1. 确保增量处理器使用 AlgorithmStorageManager 存储结果")
    logger.info("2. 或者在增量处理器中直接集成去重逻辑")
    
    logger.info("\n问题2解决方案 - 去重逻辑修复:")
    logger.info("选项A: 修改排除字段配置")
    logger.info("  - 将 wash_trading_id 加入排除字段")
    logger.info("  - 只比较核心业务字段: user_a_id, user_b_id, contract_name")
    
    logger.info("\n选项B: 修改ID生成策略")
    logger.info("  - 基于业务数据生成稳定的ID")
    logger.info("  - 相同业务数据生成相同ID")
    
    logger.info("\n推荐方案: 选项A - 修改排除字段")
    logger.info("理由: 改动最小，效果最直接")
    
    return True

def main():
    """主调试函数"""
    logger.info("开始去重问题调试")
    
    debug_functions = [
        ("哈希生成过程", debug_hash_generation),
        ("wash_trading_id 问题", debug_wash_trading_id_issue),
        ("增量vs普通模式差异", debug_incremental_vs_normal),
        ("解决方案建议", suggest_solutions)
    ]
    
    for debug_name, debug_func in debug_functions:
        try:
            logger.info(f"\n{'='*20} {debug_name} {'='*20}")
            debug_func()
        except Exception as e:
            logger.error(f"调试 {debug_name} 失败: {e}")
    
    logger.info("\n" + "="*60)
    logger.info("调试完成，请查看上述分析和建议")
    logger.info("="*60)

if __name__ == "__main__":
    main()
