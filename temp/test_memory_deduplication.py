#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存去重功能测试
测试全字段匹配去重功能、内存缓存管理和批量数据处理
"""

import sys
import os
import json
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
from backend.database.algorithm_storage_manager import AlgorithmStorageManager
from backend.database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_memory_deduplication_manager():
    """测试内存去重管理器基础功能"""
    logger.info("=== 测试内存去重管理器基础功能 ===")
    
    try:
        # 初始化管理器
        db_manager = DuckDBManager()
        dedup_manager = MemoryDeduplicationManager(db_manager)
        
        # 测试数据
        test_records = [
            {
                'task_id': 'test_task_1',
                'algorithm_type': 'wash_trading',
                'risk_level': 'medium',
                'confidence_score': 0.8,
                'trading_volume': 1000.0,
                'trading_frequency': 10,
                'indicators': '{"test": "data"}'
            },
            {
                'task_id': 'test_task_1',  # 重复记录
                'algorithm_type': 'wash_trading',
                'risk_level': 'medium',
                'confidence_score': 0.8,
                'trading_volume': 1000.0,
                'trading_frequency': 10,
                'indicators': '{"test": "data"}'
            },
            {
                'task_id': 'test_task_2',  # 不同记录
                'algorithm_type': 'high_frequency',
                'risk_level': 'high',
                'confidence_score': 0.9,
                'trading_volume': 2000.0,
                'trading_frequency': 20,
                'indicators': '{"test": "data2"}'
            }
        ]
        
        # 测试去重功能
        logger.info("测试去重功能...")
        unique_records = dedup_manager.deduplicate_records('algorithm_results', test_records)
        
        logger.info(f"原始记录数: {len(test_records)}")
        logger.info(f"去重后记录数: {len(unique_records)}")
        
        # 验证结果
        if len(unique_records) == 2:
            logger.info("✅ 去重功能测试通过")
        else:
            logger.error("❌ 去重功能测试失败")
            return False
        
        # 测试内存缓存更新
        logger.info("测试内存缓存更新...")
        dedup_manager.update_memory_cache('algorithm_results', unique_records)
        
        # 测试完整流程
        logger.info("测试完整去重流程...")
        final_records, stats = dedup_manager.process_with_deduplication('algorithm_results', test_records)
        
        logger.info(f"完整流程统计: {stats}")
        
        if stats.get('duplicate_count', 0) == 1:
            logger.info("✅ 完整去重流程测试通过")
        else:
            logger.error("❌ 完整去重流程测试失败")
            return False
        
        # 测试统计信息
        dedup_stats = dedup_manager.get_deduplication_stats()
        logger.info(f"去重统计信息: {dedup_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"内存去重管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_storage_integration():
    """测试算法存储管理器集成"""
    logger.info("=== 测试算法存储管理器集成 ===")
    
    try:
        # 初始化存储管理器
        storage_manager = AlgorithmStorageManager()
        
        # 测试数据
        test_result_data = {
            'summary': {
                'total_volume': 5000.0,
                'total_frequency': 50
            },
            'indicators': {
                'risk_score': 0.85,
                'detection_confidence': 0.9
            },
            'contract_risks': [
                {
                    'member_id': 'test_user_1',
                    'contract_name': 'BTCUSDT',
                    'detection_type': 'wash_trading',
                    'detection_method': 'same_account',
                    'risk_score': 85,
                    'abnormal_volume': 1000.0,
                    'trade_count': 10,
                    'additional_data': {
                        'long_position_id': 'pos_1',
                        'short_position_id': 'pos_2'
                    }
                }
            ]
        }
        
        # 测试存储算法结果（第一次）
        logger.info("第一次存储算法结果...")
        result_id_1 = storage_manager.store_algorithm_result(
            task_id='test_integration_1',
            algorithm_type='wash_trading_detection',
            result_data=test_result_data
        )
        
        if result_id_1:
            logger.info(f"✅ 第一次存储成功，result_id: {result_id_1}")
        else:
            logger.error("❌ 第一次存储失败")
            return False
        
        # 测试存储相同数据（应该被去重）
        logger.info("第二次存储相同数据...")
        result_id_2 = storage_manager.store_algorithm_result(
            task_id='test_integration_1',  # 相同的task_id和数据
            algorithm_type='wash_trading_detection',
            result_data=test_result_data
        )
        
        if result_id_2 is None:
            logger.info("✅ 重复数据被正确过滤")
        else:
            logger.warning(f"⚠️ 重复数据未被过滤，result_id: {result_id_2}")
        
        # 测试存储不同数据
        logger.info("存储不同数据...")
        different_data = test_result_data.copy()
        different_data['summary']['total_volume'] = 8000.0  # 修改数据
        
        result_id_3 = storage_manager.store_algorithm_result(
            task_id='test_integration_2',  # 不同的task_id
            algorithm_type='wash_trading_detection',
            result_data=different_data
        )
        
        if result_id_3:
            logger.info(f"✅ 不同数据存储成功，result_id: {result_id_3}")
        else:
            logger.error("❌ 不同数据存储失败")
            return False
        
        # 获取去重统计信息
        dedup_stats = storage_manager.dedup_manager.get_deduplication_stats()
        logger.info(f"集成测试去重统计: {dedup_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"算法存储管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processing():
    """测试批量数据处理"""
    logger.info("=== 测试批量数据处理 ===")
    
    try:
        dedup_manager = MemoryDeduplicationManager()
        
        # 生成大量测试数据
        batch_records = []
        for i in range(100):
            record = {
                'task_id': f'batch_task_{i % 10}',  # 10个不同的task_id，会有重复
                'algorithm_type': 'batch_test',
                'risk_level': 'medium',
                'confidence_score': 0.8,
                'trading_volume': float(i * 100),
                'trading_frequency': i,
                'indicators': f'{{"batch_index": {i}}}'
            }
            batch_records.append(record)
        
        logger.info(f"生成批量测试数据: {len(batch_records)} 条")
        
        # 测试批量去重
        start_time = datetime.now()
        unique_records, stats = dedup_manager.process_with_deduplication('algorithm_results', batch_records)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"批量处理完成:")
        logger.info(f"  - 处理时间: {processing_time:.3f}秒")
        logger.info(f"  - 原始记录: {stats.get('original_count', 0)}")
        logger.info(f"  - 唯一记录: {stats.get('unique_count', 0)}")
        logger.info(f"  - 重复记录: {stats.get('duplicate_count', 0)}")
        logger.info(f"  - 缓存大小: {stats.get('cache_size', 0)}")
        
        # 验证性能
        if processing_time < 1.0:  # 应该在1秒内完成
            logger.info("✅ 批量处理性能测试通过")
        else:
            logger.warning(f"⚠️ 批量处理性能较慢: {processing_time:.3f}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"批量数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始内存去重功能测试")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("内存去重管理器基础功能", test_memory_deduplication_manager()))
    test_results.append(("算法存储管理器集成", test_algorithm_storage_integration()))
    test_results.append(("批量数据处理", test_batch_processing()))
    
    # 汇总测试结果
    logger.info("\n" + "="*50)
    logger.info("测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！内存去重功能正常工作")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
