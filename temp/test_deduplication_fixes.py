#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试去重修复效果
验证两个问题的修复：
1. 去重逻辑修复（排除关联ID字段）
2. 增量算法存储修复（启用去重）
"""

import sys
import os
import json
import hashlib
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_deduplication_logic_fix():
    """测试去重逻辑修复"""
    logger.info("=== 测试去重逻辑修复 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 测试数据：相同的业务数据，不同的关联ID
        test_records = [
            {
                'id': 1,
                'wash_trading_id': 100,  # 不同的关联ID
                'user_a_id': 'test_user_1',
                'user_b_id': 'test_user_2',
                'contract_name': 'BTCUSDT',
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'wash_trading_id': 101,  # 不同的关联ID
                'user_a_id': 'test_user_1',  # 相同的业务数据
                'user_b_id': 'test_user_2',  # 相同的业务数据
                'contract_name': 'BTCUSDT',  # 相同的业务数据
                'created_at': datetime.now()
            }
        ]
        
        # 执行去重
        unique_records = dedup_manager.deduplicate_records('cross_account_wash_trading', test_records)
        
        logger.info(f"原始记录数: {len(test_records)}")
        logger.info(f"去重后记录数: {len(unique_records)}")
        
        # 验证结果
        if len(unique_records) == 1:
            logger.info("✅ 去重逻辑修复成功：相同业务数据被正确识别为重复")
            return True
        else:
            logger.error("❌ 去重逻辑修复失败：相同业务数据未被识别为重复")
            return False
        
    except Exception as e:
        logger.error(f"去重逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_results_deduplication():
    """测试 algorithm_results 表去重"""
    logger.info("=== 测试 algorithm_results 表去重 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 测试数据：相同的业务数据，不同的ID
        test_records = [
            {
                'id': 1,
                'task_id': 'test_task_1',
                'algorithm_type': 'wash_trading',
                'risk_level': 'medium',
                'confidence_score': 0.8,
                'trading_volume': 1000.0,
                'trading_frequency': 10,
                'indicators': '{"test": "data"}',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            },
            {
                'id': 2,  # 不同的ID
                'task_id': 'test_task_1',  # 相同的业务数据
                'algorithm_type': 'wash_trading',
                'risk_level': 'medium',
                'confidence_score': 0.8,
                'trading_volume': 1000.0,
                'trading_frequency': 10,
                'indicators': '{"test": "data"}',
                'created_at': datetime.now(),  # 不同的时间
                'updated_at': datetime.now()
            }
        ]
        
        # 执行去重
        unique_records = dedup_manager.deduplicate_records('algorithm_results', test_records)
        
        logger.info(f"原始记录数: {len(test_records)}")
        logger.info(f"去重后记录数: {len(unique_records)}")
        
        # 验证结果
        if len(unique_records) == 1:
            logger.info("✅ algorithm_results 表去重成功")
            return True
        else:
            logger.error("❌ algorithm_results 表去重失败")
            return False
        
    except Exception as e:
        logger.error(f"algorithm_results 表去重测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wash_trading_results_deduplication():
    """测试 wash_trading_results 表去重"""
    logger.info("=== 测试 wash_trading_results 表去重 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 测试数据：相同的业务数据，不同的关联ID
        test_records = [
            {
                'id': 1,
                'algorithm_result_id': 100,  # 不同的关联ID
                'trading_type': 'wash_trading',
                'pair_count': 5,
                'total_volume': 1000.0,
                'avg_time_gap': 0,
                'risk_score': 0.8,
                'detection_method': 'comprehensive_analysis',
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'algorithm_result_id': 101,  # 不同的关联ID
                'trading_type': 'wash_trading',  # 相同的业务数据
                'pair_count': 5,
                'total_volume': 1000.0,
                'avg_time_gap': 0,
                'risk_score': 0.8,
                'detection_method': 'comprehensive_analysis',
                'created_at': datetime.now()
            }
        ]
        
        # 执行去重
        unique_records = dedup_manager.deduplicate_records('wash_trading_results', test_records)
        
        logger.info(f"原始记录数: {len(test_records)}")
        logger.info(f"去重后记录数: {len(unique_records)}")
        
        # 验证结果
        if len(unique_records) == 1:
            logger.info("✅ wash_trading_results 表去重成功")
            return True
        else:
            logger.error("❌ wash_trading_results 表去重失败")
            return False
        
    except Exception as e:
        logger.error(f"wash_trading_results 表去重测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_check():
    """测试配置检查"""
    logger.info("=== 测试配置检查 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 检查配置
        config = dedup_manager.target_tables
        
        logger.info("当前去重配置:")
        for table_name, table_config in config.items():
            exclude_fields = table_config.get('exclude_fields', [])
            logger.info(f"  {table_name}: 排除字段 {exclude_fields}")
        
        # 验证关键配置
        expected_excludes = {
            'algorithm_results': ['id', 'created_at', 'updated_at'],
            'wash_trading_results': ['id', 'created_at', 'algorithm_result_id'],
            'same_account_wash_trading': ['id', 'created_at', 'wash_trading_id'],
            'cross_account_wash_trading': ['id', 'created_at', 'wash_trading_id']
        }
        
        all_correct = True
        for table_name, expected_fields in expected_excludes.items():
            actual_fields = config[table_name]['exclude_fields']
            if set(actual_fields) != set(expected_fields):
                logger.error(f"❌ {table_name} 配置错误: 期望 {expected_fields}, 实际 {actual_fields}")
                all_correct = False
            else:
                logger.info(f"✅ {table_name} 配置正确")
        
        return all_correct
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始去重修复效果测试")
    
    test_functions = [
        ("配置检查", test_configuration_check),
        ("去重逻辑修复", test_deduplication_logic_fix),
        ("algorithm_results表去重", test_algorithm_results_deduplication),
        ("wash_trading_results表去重", test_wash_trading_results_deduplication)
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("去重修复效果测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 去重修复效果测试全部通过！")
        logger.info("\n修复总结:")
        logger.info("1. ✅ 去重逻辑已修复：排除关联ID字段，只比较业务字段")
        logger.info("2. ✅ 增量算法已修复：增加存储逻辑，启用去重功能")
        logger.info("3. ✅ 配置已优化：各表排除字段配置正确")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查修复效果")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
