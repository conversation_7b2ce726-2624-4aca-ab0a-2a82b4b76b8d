我突然有一个更好的思路来去重，或者说传入数据的唯一性，写入之前先用内存加载duckdb里面我们需要去重的数据表格内容，这些内容的数据量都只有几千条，最多几万条数据量，所以既然能确保数据量不大的情况下，我们加载到内存里面去重以后，再只写入去重以后的数据，是不是就能实现，你分析一下可能性## 合约分析引擎数据落库与去重方案（对敲交易 / 高频交易 / 套资金费率）

本文档梳理现有合约分析引擎的生成数据、落库表结构及建议的“高性能去重”方案，覆盖对敲交易（wash trading）、高频交易（high frequency）与资金费率套利（funding rate arbitrage）。

- 代码位置概览：
  - 引擎入口与API：backend/modules/contract_risk_analysis/
  - 主分析器：services/contract_analyzer.py（类 CTContractAnalyzer）
  - 对敲检测核心：optimizers/position_based_optimizer.py（第四代 PositionBasedOptimizer，包含对敲评分/时间窗口/金额匹配等）
  - 高频/资金费率配置：config/algorithms_config.py 与 config/config/algorithms.json
  - 数据库与表初始化：backend/database/duckdb_manager.py、manage_duckbd/create_database.py、backend/database/schema/

- 现有（或文档中列出的）相关表：
  - algorithm_results（算法结果主表，统一汇聚）
  - wash_trading_results（对敲交易“统计/汇总”表）
  - same_account_wash_trading（同账户对敲详情）
  - cross_account_wash_trading（跨账户对敲详情）
  - high_frequency_trading_details（高频交易详情）
  - funding_rate_arbitrage_details（资金费率套利详情）
  - contract_risk_analysis（兼容性表，JSON 备份/适配）
  - user_trading_profiles（用户交易画像，已知 UNIQUE(member_id, analysis_date)）

备注：后续精细化以实际 schema 为准；本文基于代码与命名约定给出准确且稳健的“唯一键/去重键”建议与写入策略。

---

## 1. 引擎与数据流概览

- 上传/同步数据经 API（contract_api.py）进入 CTContractAnalyzer
- 普通模式路径：
  1) _standardize_fields 标准化字段
  2) PositionBasedOptimizer 生成完整持仓、窗口索引、检测（含对敲）
  3) 风险评估/聚合
  4) 存储层落库
- 增量模式：跳过部分预处理，直接用预构建的订单/持仓进行检测

输出侧通常包含：
- 详情：对敲交易配对记录、高频窗口记录、资金费率套利窗口记录
- 统计/汇总：按用户/合约/时间窗/风险类型的聚合度量
- 用户画像：写入 user_trading_profiles 的画像指标（如 wash_trading_volume、high_frequency_volume、funding_arbitrage_volume 等）

---

## 2. 各表数据格式与“唯一键/去重键”建议

以下给出每个表“字段与典型数据”的建议说明，以及“为确保无重复”应抓取的关键词（组合键），并附规范化规则，便于在内存中构造 canonical key（标准化键）。

说明：
- 规范化规则统一：
  - 字符串：trim、lower；
  - 时间：统一到秒（或实际使用精度的一致粒度，例如秒/毫秒，选定后全局一致）；
  - 金额/数量：按表字段精度（DECIMAL）进行量化/四舍五入，避免小数精度差导致“同值不同表示”。
- 若“同键冲突”的业务策略为“跳过重复”，则使用 NOT EXISTS；若为“更新已有”，则使用 MERGE 的 WHEN MATCHED THEN UPDATE（DuckDB 版本支持时）。

### 2.1 algorithm_results（算法结果主表）
用途：各算法的统一结果汇聚（便于统一查询/看板）。

建议字段（示意）：
- detection_type（如 'wash_trading'/'high_frequency'/'funding_rate_arbitrage'）
- member_id、contract_name（或 symbol）
- window_start、window_end（时间窗口类算法）或 event_time（事件类）
- risk_score、abnormal_volume 等指标
- task_id（分析任务）、created_at

建议唯一键（去重键）：
- detection_type + member_id + contract_name + window_start + window_end
- 非窗口类（单事件）：detection_type + member_id + contract_name + event_time + signature
  - signature 可为内容哈希（关键数值/方向/价格/数量的稳定组合）

### 2.2 wash_trading_results（对敲交易统计/汇总）
用途：对敲结果的聚合（例如用户维度、日维度/合约维度的汇总计数与成交额）。

建议字段（示意）：
- member_id、contract_name、stat_date（或 window_start/window_end）
- wash_pairs_count、wash_trading_volume、avg_wash_score
- created_at

建议唯一键：
- member_id + contract_name + stat_date（按日汇总）
- 或 member_id + contract_name + window_start + window_end（按窗口汇总）

### 2.3 same_account_wash_trading（同账户对敲详情）
用途：单账户内“自对敲”配对详情（PositionBasedOptimizer 中 pos_a/pos_b 配对形成）。

建议字段（示意）：
- member_id、contract_name
- trade_a_time、trade_b_time（或 first_open_time/last_close_time 等关键时间）
- trade_a_side、trade_b_side（多/空）、amount_a、amount_b
- price_a、price_b、wash_score
- pair_id（建议生成的稳定配对ID，见下）

建议唯一键：
- pair_id（稳定生成，避免重复插入）
- 若无 pair_id，则：member_id + contract_name + min(time_a,time_b)_bucket + max(time_a,time_b)_bucket + amount_bucket
  - time_bucket：按检测时间窗口精度（秒）量化
  - amount_bucket：按金额容差（position_based_optimizer 中 amount_match_tolerance）量化后的归一值

pair_id 生成建议（稳定、无序对等价）：
- 对同账户：hash(member_id, contract_name, round_to_sec(time_a), round_to_sec(time_b), quantized_amount_pair)
  - 其中 time_a < time_b 的有序化处理或对 (time_a,time_b) 排序后拼接，确保交换不影响ID

### 2.4 cross_account_wash_trading（跨账户对敲详情）
用途：两个不同账户之间的对敲配对详情。

建议字段（示意）：
- member_id, counterpart_member_id（两端账户，按字典序升序存储）
- contract_name、time_a、time_b、side_a、side_b
- amount_a、amount_b、price_a、price_b、wash_score
- pair_id（稳定配对ID）

建议唯一键：
- pair_id
- 若无 pair_id，则：unordered_pair(member_id, counterpart_member_id) + contract_name + time_bucket_pair + amount_bucket
  - unordered_pair：将两端ID排序后拼接，避免顺序影响

pair_id 生成建议：
- hash(sorted(member_id, counterpart_member_id), contract_name, round_to_sec(time_a), round_to_sec(time_b), quantized_amount_pair)

### 2.5 high_frequency_trading_details（高频交易详情）
用途：在固定窗口内交易频次/市价单比率/持仓时长等指标，标记高频行为。

建议字段（示意）：
- member_id、contract_name
- window_start、window_end
- trades_count、frequency_per_minute、market_order_ratio、avg_holding_time
- risk_level、risk_score

建议唯一键：
- member_id + contract_name + window_start + window_end

备注：确保 window 对齐策略（algorithms.json 的 time_window_minutes/step_minutes）在键构造中保持一致，避免错位窗口导致重复/漏判。

### 2.6 funding_rate_arbitrage_details（资金费率套利详情）
用途：围绕资金费率周期（通常 8h/1h）与持仓方向/收益相关的窗口检测。

建议字段（示意）：
- member_id、contract_name
- funding_window_start、funding_window_end 或 funding_time（周期边界）
- position_side、exposure、expected_funding_pnl、realized_pnl
- correlation/相关性度量、risk_score

建议唯一键：
- member_id + contract_name + funding_window_start + funding_window_end + position_side

### 2.7 contract_risk_analysis（兼容性表，JSON）
用途：历史/兼容存储，JSON 形式保存检测结果。

建议唯一键：
- member_id + detection_type + contract_name + window_start + window_end
- 或 member_id + detection_type + json_signature（对 JSON 关键内容进行稳定哈希）

### 2.8 user_trading_profiles（画像汇总）
- 已存在唯一约束：UNIQUE(member_id, analysis_date)
- 对应字段包含：wash_trading_volume、high_frequency_volume、funding_arbitrage_volume 等
- 去重策略：严格以 (member_id, analysis_date) 为键；若需要“同键更新”，用 MERGE 更新汇总字段。

---

## 3. 高性能去重与写入流程（内存预检 + 数据库兜底）

总体思路：
- 启动时加载目标表“唯一键集合”到内存 HashSet（几千～几万条，轻量）
- 新批次生成 canonical key 进行预过滤，得到 uniques 待写集合
- 批量写入使用“单条 SQL + 反连接（NOT EXISTS）”做强一致兜底，避免并发/竞态重复
- 成功写入后再把“本批实际写入的键”并入内存集合

### 3.1 键的规范化（canonical key）
- 统一大小写与空白：lower(trim(x))
- 时间统一精度：round_to_seconds(ts) 或按算法窗口粒度归一
- 数值统一精度：按字段 DECIMAL 精度 round/quantize
- 组合键拼接：使用固定顺序与分隔符（如 '#\u001f#' 不常见分隔，减少碰撞），或直接计算哈希（SHA-256）

示例（Python 伪代码）：
- same_account_wash_trading：
  - key = hash(member_id, contract, sort([t_a_sec, t_b_sec]), quantize_pair(amount_a, amount_b))
- high_frequency_trading_details：
  - key = f"{member_id}#{contract}#{win_start_sec}#{win_end_sec}"

### 3.2 三种“批量·单SQL写入”方式（择一）
- A. 注册内存批次为 DuckDB relation（最快）
  - INSERT INTO target SELECT ... FROM batch v WHERE NOT EXISTS (SELECT 1 FROM target t WHERE key_cond);
- B. 临时 Parquet/CSV + read_parquet/read_csv_auto（稳妥）
  - INSERT INTO target SELECT ... FROM read_parquet('temp/batch.parquet') v WHERE NOT EXISTS (...);
- C. 单SQL内联 VALUES（小批次时）
  - INSERT INTO target (...) SELECT ... FROM (VALUES (...),(...)) v(cols...) WHERE NOT EXISTS (...);

批量大小建议：
- 1k–5k 行/批较稳妥；VALUES 方案注意 SQL 文本长度控制；A/B 方案可更大一些

### 3.3 DB 兜底条件（NOT EXISTS/MERGE）
- same_account_wash_trading：
  - NOT EXISTS (SELECT 1 FROM same_account_wash_trading t WHERE t.pair_id = v.pair_id)
- cross_account_wash_trading：
  - NOT EXISTS (SELECT 1 FROM cross_account_wash_trading t WHERE t.pair_id = v.pair_id)
- high_frequency_trading_details：
  - NOT EXISTS (SELECT 1 FROM high_frequency_trading_details t WHERE t.member_id=v.member_id AND t.contract=v.contract AND t.window_start=v.window_start AND t.window_end=v.window_end)
- funding_rate_arbitrage_details：
  - NOT EXISTS (SELECT 1 FROM funding_rate_arbitrage_details t WHERE t.member_id=v.member_id AND t.contract=v.contract AND t.funding_window_start=v.funding_window_start AND t.funding_window_end=v.funding_window_end AND t.position_side=v.position_side)
- algorithm_results（汇总）：
  - NOT EXISTS (SELECT 1 FROM algorithm_results t WHERE t.detection_type=v.detection_type AND t.member_id=v.member_id AND t.contract=v.contract AND t.window_start=v.window_start AND t.window_end=v.window_end)
- contract_risk_analysis（JSON 兼容）：
  - NOT EXISTS (SELECT 1 FROM contract_risk_analysis t WHERE t.member_id=v.member_id AND t.detection_type=v.detection_type AND t.window_start=v.window_start AND t.window_end=v.window_end)
- user_trading_profiles：
  - MERGE 或 NOT EXISTS 以 (member_id, analysis_date) 为键

### 3.4 成功后更新内存键集
- 仅在 SQL 执行成功后，把“本批待写键集合”并入内存 HashSet
- 若 DuckDB 支持 INSERT ... RETURNING，可直接获取“实际插入行”精确更新

---

## 4. 对三类检测的专属去重要点

### 4.1 对敲交易（wash trading）
- 时间窗口：position_based_optimizer.config['wash_trading_time_window']（例如 30 秒）
- 金额容差：amount_match_tolerance（例如 0.02）
- 唯一性：核心在“配对”的稳定识别（pair_id）。务必对 (time_a,time_b) 做排序/量化，对 (amount_a,amount_b) 做“匹配后量化”以吸收容差；跨账户需要对 (member_id, counterpart) 做无序对处理。

### 4.2 高频交易（high frequency）
- 窗口定义：algorithms.json 里 time_window_minutes/step_minutes
- 唯一性：窗口粒度 + 用户 + 合约。注意“滑动窗口”下相邻窗口会重叠，属于不同键，不应相互去重。

### 4.3 资金费率套利（funding rate arbitrage）
- 窗口/周期：time_window_minutes 或资金费率出账周期
- 唯一性：用户 + 合约 + 资金费率窗口 + 持仓方向（或策略标签）

---

## 5. 建议的落地实现步骤

1) 定义“唯一键构造器”模块（集中配置）
- 输入表名 + 行对象 -> 输出 canonical key（字符串或哈希）
- 将规范化规则、时间/金额量化、无序对排序、窗口对齐等固化在此处

2) 启动时初始化内存键集
- 对每个表执行 SELECT 读取唯一键必要字段（仅取键列），几千～几万行毫秒级
- 放入 HashSet

3) 批次处理：
- 先用 HashSet 预过滤，切出 uniques 子集
- 选择 A/B/C 之一进行“单SQL批量插入 + NOT EXISTS”
- 成功后更新 HashSet

4) 并发与一致性
- 单实例即可满足；多实例并发时 NOT EXISTS/MERGE 是必须兜底
- INSERT/MERGE 使用单语句执行，满足你系统“单SQL一次执行”的限制

5) 画像写入（user_trading_profiles）
- 按 (member_id, analysis_date) 做 MERGE 更新当日画像指标（wash/high_freq/funding 三项合计）

---

## 6. 验证与回归

- 小批真实数据（不造模拟数据）在 temp/ 下做预检/写入演练：
  - temp/data/：放置脱敏小样本 parquet/csv
  - temp/notebooks/：可选 Jupyter 演示去重前后数量对比
  - temp/tests/：单元/集成测试，覆盖 pair_id 稳定性、窗口对齐、NOT EXISTS 兜底有效性
- 关键校验：
  - 重复样本应被过滤（内存与 DB 侧双重确认）
  - 画像表 MERGE 后同日仅一条记录被更新
  - 读回计数与预期一致（无重复增长）

---

## 7. 需要你确认的点

- 各表最终的“唯一键字段”是否采用本文建议？
- 对敲 pair_id 是否作为标准主键列（推荐新增/统一）？
- 高频/资金费率窗口的对齐粒度（精确到秒/分钟）确认
- 同键冲突策略：跳过 vs 更新（不同表可不同策略）
- 是否需要在接口层提供“预检报告”（仅列出重复/可写，不落库）

---

## 8. 后续实施计划（建议）

- 提交一个“去重缓存层 + 批量写入器”的最小改动 PR：
  - 统一唯一键构造模块
  - 写入路径替换为“单SQL批量插入 + NOT EXISTS/MERGE”
  - 增加 temp/tests 覆盖
- 完成后清理旧的散落去重逻辑，减少维护面



---

## 附录A：基于当前实际 schema 的字段映射与最终去重键清单

以下依据 backend/database/schema/algorithm_tables.sql 与 user_trading_profiles_unified.sql 的实际字段，给出每张表的“推荐去重键（DB侧 NOT EXISTS 条件）”，并说明与文中通用建议的对齐关系。

注意：algorithm_* 系列表使用 user_id 命名；画像表使用 member_id。请在落库适配层保持 user_id ⇄ member_id 的一致映射。

### A.1 algorithm_results（通用算法结果表）
- 关键字段：algorithm_type, user_id, contract_name, time_window_start, time_window_end
- 推荐去重键（避免重复结果）：
  - (algorithm_type, user_id, contract_name, time_window_start, time_window_end)
- DB 侧兜底（示例）：
  - NOT EXISTS (
    SELECT 1 FROM algorithm_results t
    WHERE t.algorithm_type = v.algorithm_type
      AND t.user_id = v.user_id
      AND t.contract_name = v.contract_name
      AND t.time_window_start = v.time_window_start
      AND t.time_window_end = v.time_window_end
    )

### A.2 wash_trading_results（对敲交易“汇总/统一管理表”）
- 关键字段：algorithm_result_id, trading_type
- 推荐去重键：
  - (algorithm_result_id, trading_type)
- DB 侧兜底：
  - NOT EXISTS (
    SELECT 1 FROM wash_trading_results t
    WHERE t.algorithm_result_id = v.algorithm_result_id
      AND t.trading_type = v.trading_type
    )

### A.3 same_account_wash_trading（同账户对敲详情表）
- 关键字段：wash_trading_id, user_id, contract_name, long_position_id, short_position_id,
  long_open_time, short_open_time, long_volume, short_volume, long_price, short_price
- 推荐去重键优先级：
  1) 若同时存在 long_position_id 与 short_position_id：
     - (wash_trading_id, long_position_id, short_position_id)
  2) 否则使用“规范化配对签名”：
     - (wash_trading_id, user_id, contract_name,
        min(round_sec(long_open_time), round_sec(short_open_time)),
        max(round_sec(long_open_time), round_sec(short_open_time)),
        quantize_pair(long_volume, short_volume), quantize_pair(long_price, short_price))
- DB 侧兜底（示例-用位置ID）：
  - NOT EXISTS (
    SELECT 1 FROM same_account_wash_trading t
    WHERE t.wash_trading_id = v.wash_trading_id
      AND t.long_position_id <=> v.long_position_id
      AND t.short_position_id <=> v.short_position_id
    )
  注：若需基于时间/金额桶做匹配，建议在写入前就构造出 pair_key（字符串）列，DB 侧用 pair_key 做等值判断。

### A.4 cross_account_wash_trading（跨账户对敲详情表）
- 关键字段：wash_trading_id, user_a_id, user_b_id, contract_name,
  trade_a_id, trade_b_id, trade_a_time, trade_b_time, trade_a_volume, trade_b_volume
- 推荐去重键优先级：
  1) 若存在交易ID：
     - (wash_trading_id, unordered(trade_a_id, trade_b_id))
  2) 否则使用无序用户对 + 规范化窗口：
     - (wash_trading_id, unordered(user_a_id, user_b_id), contract_name,
        min(round_sec(trade_a_time), round_sec(trade_b_time)),
        max(round_sec(trade_a_time), round_sec(trade_b_time)),
        quantize_pair(trade_a_volume, trade_b_volume))
- 实现建议：在应用层生成 pair_id（稳定无序配对签名），落库用 pair_id 列去重最稳。

### A.5 high_frequency_trading_details（高频交易详情表）
- 关键字段：algorithm_result_id, user_id, contract_name, trade_count, max_frequency 等
- 推荐去重键：
  - 首选 (algorithm_result_id)
  - 如存在一个结果拆多行，则 (algorithm_result_id, user_id, contract_name)
- DB 侧兜底：
  - NOT EXISTS (
    SELECT 1 FROM high_frequency_trading_details t
    WHERE t.algorithm_result_id = v.algorithm_result_id
    )
  或（更稳）：
  - NOT EXISTS (
    SELECT 1 FROM high_frequency_trading_details t
    WHERE t.algorithm_result_id = v.algorithm_result_id
      AND t.user_id = v.user_id
      AND t.contract_name = v.contract_name
    )

### A.6 funding_rate_arbitrage_details（资金费率套利详情表）
- 关键字段：algorithm_result_id, user_id, contract_name, funding_rate, position_size, holding_duration
- 推荐去重键：
  - 首选 (algorithm_result_id)
  - 备选 (algorithm_result_id, user_id, contract_name)
- DB 侧兜底：
  - NOT EXISTS (
    SELECT 1 FROM funding_rate_arbitrage_details t
    WHERE t.algorithm_result_id = v.algorithm_result_id
    )

### A.7 contract_risk_analysis（兼容性视图/表）
- 若需去重，请以 algorithm_results 的键为基准做签名（见 A.1），或对 JSON 关键域做稳定哈希 json_signature。

### A.8 user_trading_profiles（画像表）
- 实际约束：UNIQUE(member_id, analysis_date)
- 策略：使用 MERGE 做“同键更新”；若仅插入、则 NOT EXISTS 过滤同键。

---

## 附录B：内存去重键加载建议（按实际字段）

- algorithm_results：加载 (algorithm_type, user_id, contract_name, time_window_start, time_window_end)
- wash_trading_results：加载 (algorithm_result_id, trading_type)
- same_account_wash_trading：优先加载 (wash_trading_id, long_position_id, short_position_id)；若为空，加载 pair_key（应用层生成）
- cross_account_wash_trading：优先加载 (wash_trading_id, unordered(trade_a_id, trade_b_id))；若为空，加载 pair_key
- high_frequency_trading_details：加载 algorithm_result_id（或附加 user_id, contract_name）
- funding_rate_arbitrage_details：加载 algorithm_result_id（或附加 user_id, contract_name）
- user_trading_profiles：加载 (member_id, analysis_date)

说明：pair_key 为应用层稳定签名，建议统一生成规则与函数，保证前后端及不同算法版本一致。
