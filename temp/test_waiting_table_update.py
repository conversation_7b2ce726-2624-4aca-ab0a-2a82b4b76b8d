#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试等待表更新修复
验证 incomplete_positions_waiting 表的 UPDATE 操作是否正常
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_waiting_table_update():
    """测试等待表更新操作"""
    logger.info("=== 测试等待表更新操作 ===")
    
    try:
        from backend.database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager()
        
        # 1. 检查表是否存在
        logger.info("检查 incomplete_positions_waiting 表...")
        check_table_sql = """
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_name = 'incomplete_positions_waiting'
        """
        
        result = db_manager.execute_sql(check_table_sql)
        if result[0]['table_count'] == 0:
            logger.warning("incomplete_positions_waiting 表不存在")
            return False
        
        logger.info("✅ incomplete_positions_waiting 表存在")
        
        # 2. 查看表中的数据
        count_sql = "SELECT COUNT(*) as total_count FROM incomplete_positions_waiting"
        count_result = db_manager.execute_sql(count_sql)
        total_records = count_result[0]['total_count']
        
        logger.info(f"表中总记录数: {total_records}")
        
        if total_records == 0:
            logger.info("表中没有数据，无法测试更新操作")
            return True
        
        # 3. 查看一些示例数据
        sample_sql = """
        SELECT position_id, last_check_time, check_count 
        FROM incomplete_positions_waiting 
        LIMIT 5
        """
        
        sample_data = db_manager.execute_sql(sample_sql)
        logger.info("示例数据:")
        for record in sample_data:
            logger.info(f"  position_id: {record['position_id']}, last_check_time: {record['last_check_time']}, check_count: {record['check_count']}")
        
        # 4. 测试安全的更新操作
        logger.info("测试安全的更新操作...")
        
        # 计算时间
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)
        now_formatted = now.strftime('%Y-%m-%d %H:%M:%S')
        one_hour_ago_formatted = one_hour_ago.strftime('%Y-%m-%d %H:%M:%S')
        
        # 先查询需要更新的记录
        query_sql = """
        SELECT position_id FROM incomplete_positions_waiting
        WHERE last_check_time < ?
        """
        
        records_to_update = db_manager.execute_sql(query_sql, [one_hour_ago_formatted])
        logger.info(f"需要更新的记录数: {len(records_to_update)}")
        
        if records_to_update:
            # 逐个更新，避免批量UPDATE的主键冲突问题
            update_sql = """
            UPDATE incomplete_positions_waiting
            SET last_check_time = ?, check_count = check_count + 1
            WHERE position_id = ?
            """
            
            updated_count = 0
            failed_count = 0
            
            for record in records_to_update[:3]:  # 只测试前3条记录
                try:
                    db_manager.execute_sql(update_sql, [now_formatted, record['position_id']])
                    updated_count += 1
                    logger.info(f"✅ 成功更新记录: {record['position_id']}")
                except Exception as single_update_error:
                    failed_count += 1
                    logger.error(f"❌ 更新记录失败 {record['position_id']}: {single_update_error}")
            
            logger.info(f"更新结果: 成功 {updated_count} 条, 失败 {failed_count} 条")
            
            if failed_count == 0:
                logger.info("✅ 等待表更新测试通过")
                return True
            else:
                logger.error("❌ 等待表更新测试部分失败")
                return False
        else:
            logger.info("✅ 没有需要更新的记录，测试通过")
            return True
        
    except Exception as e:
        logger.error(f"等待表更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_primary_key_constraint():
    """测试主键约束"""
    logger.info("=== 测试主键约束 ===")
    
    try:
        from backend.database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager()
        
        # 测试插入重复的主键
        test_position_id = f"test_pk_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        insert_sql = """
        INSERT INTO incomplete_positions_waiting
        (position_id, member_id, contract_name, primary_side, first_open_time,
         total_open_amount, open_trades_count, avg_open_price, total_open_volume,
         waiting_since, last_check_time, check_count, source_task_id, data_version,
         created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        test_data = [
            test_position_id,
            'test_user',
            'BTCUSDT',
            1,
            datetime.now(),
            1000.0,
            1,
            50000.0,
            1000.0,
            datetime.now(),
            datetime.now(),
            0,
            'test_task',
            '1.0',
            datetime.now(),
            datetime.now()
        ]
        
        # 第一次插入应该成功
        try:
            db_manager.execute_sql(insert_sql, test_data)
            logger.info(f"✅ 第一次插入成功: {test_position_id}")
        except Exception as e:
            logger.error(f"❌ 第一次插入失败: {e}")
            return False
        
        # 第二次插入相同主键应该失败
        try:
            db_manager.execute_sql(insert_sql, test_data)
            logger.error(f"❌ 第二次插入应该失败但成功了: {test_position_id}")
            return False
        except Exception as e:
            logger.info(f"✅ 第二次插入正确失败: {e}")
        
        # 清理测试数据
        cleanup_sql = "DELETE FROM incomplete_positions_waiting WHERE position_id = ?"
        db_manager.execute_sql(cleanup_sql, [test_position_id])
        logger.info(f"✅ 清理测试数据: {test_position_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"主键约束测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始等待表更新修复测试")
    
    test_functions = [
        ("等待表更新操作", test_waiting_table_update),
        ("主键约束测试", test_primary_key_constraint)
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("等待表更新修复测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 等待表更新修复测试全部通过！")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
