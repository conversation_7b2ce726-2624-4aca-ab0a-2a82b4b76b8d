#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启用内存去重功能
提供逐步测试和启用去重功能的工具
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_deduplication_status():
    """检查去重功能状态"""
    logger.info("=== 检查去重功能状态 ===")
    
    try:
        # 这里我们只检查配置，不实际导入可能有问题的模块
        logger.info("检查去重功能配置...")
        
        # 读取配置文件内容
        config_file = "backend/database/algorithm_storage_manager.py"
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查去重配置
        if "'enable_memory_deduplication': False" in content:
            logger.info("✅ 内存去重功能当前已禁用（安全状态）")
            return "disabled"
        elif "'enable_memory_deduplication': True" in content:
            logger.info("⚠️ 内存去重功能当前已启用")
            return "enabled"
        else:
            logger.warning("❓ 无法确定去重功能状态")
            return "unknown"
        
    except Exception as e:
        logger.error(f"检查去重功能状态失败: {e}")
        return "error"

def enable_deduplication_safely():
    """安全启用去重功能"""
    logger.info("=== 安全启用去重功能 ===")
    
    try:
        # 读取当前配置
        config_file = "backend/database/algorithm_storage_manager.py"
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查当前状态
        if "'enable_memory_deduplication': True" in content:
            logger.info("内存去重功能已经启用")
            return True
        
        # 启用去重功能
        new_content = content.replace(
            "'enable_memory_deduplication': False  # 暂时禁用内存去重，避免事务冲突",
            "'enable_memory_deduplication': True  # 启用内存去重"
        )
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info("✅ 内存去重功能已启用")
        return True
        
    except Exception as e:
        logger.error(f"启用去重功能失败: {e}")
        return False

def disable_deduplication_safely():
    """安全禁用去重功能"""
    logger.info("=== 安全禁用去重功能 ===")
    
    try:
        # 读取当前配置
        config_file = "backend/database/algorithm_storage_manager.py"
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查当前状态
        if "'enable_memory_deduplication': False" in content:
            logger.info("内存去重功能已经禁用")
            return True
        
        # 禁用去重功能
        new_content = content.replace(
            "'enable_memory_deduplication': True  # 启用内存去重",
            "'enable_memory_deduplication': False  # 暂时禁用内存去重，避免事务冲突"
        )
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info("✅ 内存去重功能已禁用")
        return True
        
    except Exception as e:
        logger.error(f"禁用去重功能失败: {e}")
        return False

def test_basic_storage_without_deduplication():
    """测试基础存储功能（不使用去重）"""
    logger.info("=== 测试基础存储功能（不使用去重）===")
    
    try:
        # 确保去重功能禁用
        disable_deduplication_safely()
        
        logger.info("基础存储功能测试需要在实际环境中运行")
        logger.info("请在系统中执行一次算法分析来验证基础功能")
        
        return True
        
    except Exception as e:
        logger.error(f"基础存储功能测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    logger.info("\n" + "="*60)
    logger.info("内存去重功能使用指南")
    logger.info("="*60)
    
    logger.info("""
🔧 配置管理:
  python3 temp/safe_enable_deduplication.py --status    # 检查状态
  python3 temp/safe_enable_deduplication.py --enable    # 启用去重
  python3 temp/safe_enable_deduplication.py --disable   # 禁用去重

📊 使用建议:
  1. 当前去重功能已禁用，系统运行正常
  2. 建议先在测试环境验证基础功能
  3. 确认无问题后再启用去重功能
  4. 可以通过API动态启用/禁用去重

🚀 动态启用（推荐）:
  # 在Python代码中
  storage_manager = AlgorithmStorageManager()
  storage_manager.enable_memory_deduplication()  # 启用
  storage_manager.disable_memory_deduplication() # 禁用

📈 监控指标:
  # 获取去重统计
  stats = storage_manager.get_deduplication_stats()
  print(stats)
""")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='内存去重功能管理工具')
    parser.add_argument('--status', action='store_true', help='检查去重功能状态')
    parser.add_argument('--enable', action='store_true', help='启用去重功能')
    parser.add_argument('--disable', action='store_true', help='禁用去重功能')
    parser.add_argument('--test', action='store_true', help='测试基础存储功能')
    parser.add_argument('--guide', action='store_true', help='显示使用指南')
    
    args = parser.parse_args()
    
    if args.status:
        status = check_deduplication_status()
        logger.info(f"当前状态: {status}")
    elif args.enable:
        success = enable_deduplication_safely()
        if success:
            logger.info("🎉 去重功能启用成功")
        else:
            logger.error("❌ 去重功能启用失败")
    elif args.disable:
        success = disable_deduplication_safely()
        if success:
            logger.info("🛡️ 去重功能禁用成功")
        else:
            logger.error("❌ 去重功能禁用失败")
    elif args.test:
        test_basic_storage_without_deduplication()
    elif args.guide:
        show_usage_guide()
    else:
        # 默认显示状态和指南
        status = check_deduplication_status()
        show_usage_guide()

if __name__ == "__main__":
    main()
