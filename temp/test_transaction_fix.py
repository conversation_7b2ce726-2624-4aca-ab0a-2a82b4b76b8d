#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试事务修复
验证去重逻辑在事务中正常工作
"""

import sys
import os
import json
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_transaction_compatibility():
    """测试事务兼容性"""
    logger.info("=== 测试事务兼容性 ===")
    
    try:
        # 模拟事务环境下的去重测试
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        # 创建去重管理器
        dedup_manager = MemoryDeduplicationManager()
        
        # 模拟数据库连接对象
        class MockConnection:
            def __init__(self):
                self.description = [('id',), ('task_id',), ('algorithm_type',), ('created_at',)]
                self.data = []
            
            def execute(self, sql):
                logger.info(f"模拟执行SQL: {sql[:50]}...")
                return self
            
            def fetchone(self):
                if 'COUNT' in str(self.last_sql):
                    return [1]  # 表存在
                return [1]
            
            def fetchall(self):
                # 返回模拟的表数据
                return [
                    (1, 'existing_task', 'wash_trading', datetime.now()),
                    (2, 'existing_task_2', 'high_frequency', datetime.now())
                ]
            
            def __getattr__(self, name):
                if name == 'last_sql':
                    return getattr(self, '_last_sql', '')
                return lambda *args, **kwargs: self
        
        mock_conn = MockConnection()
        
        # 测试数据
        test_records = [
            {
                'id': 3,
                'task_id': 'new_task',
                'algorithm_type': 'wash_trading',
                'created_at': datetime.now()
            },
            {
                'id': 4,
                'task_id': 'existing_task',  # 这个应该被去重
                'algorithm_type': 'wash_trading',
                'created_at': datetime.now()
            }
        ]
        
        # 测试在事务中使用去重
        logger.info("测试在模拟事务中使用去重...")
        unique_records, stats = dedup_manager.process_with_deduplication(
            'algorithm_results', 
            test_records, 
            mock_conn
        )
        
        logger.info(f"去重结果: {stats}")
        logger.info(f"唯一记录数: {len(unique_records)}")
        
        # 验证结果
        if len(unique_records) <= len(test_records):
            logger.info("✅ 事务兼容性测试通过")
            return True
        else:
            logger.error("❌ 事务兼容性测试失败")
            return False
        
    except Exception as e:
        logger.error(f"事务兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_parameter():
    """测试连接参数传递"""
    logger.info("=== 测试连接参数传递 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 测试不传连接参数（应该创建新连接）
        logger.info("测试不传连接参数...")
        try:
            result1 = dedup_manager.load_table_to_memory('algorithm_results')
            logger.info(f"不传连接参数结果: {result1}")
        except Exception as e:
            logger.info(f"不传连接参数失败（预期）: {e}")
        
        # 测试传递None连接参数
        logger.info("测试传递None连接参数...")
        try:
            result2 = dedup_manager.load_table_to_memory('algorithm_results', None)
            logger.info(f"传递None连接参数结果: {result2}")
        except Exception as e:
            logger.info(f"传递None连接参数失败（预期）: {e}")
        
        logger.info("✅ 连接参数传递测试完成")
        return True
        
    except Exception as e:
        logger.error(f"连接参数传递测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_signatures():
    """测试方法签名兼容性"""
    logger.info("=== 测试方法签名兼容性 ===")
    
    try:
        from backend.database.memory_deduplication_manager import MemoryDeduplicationManager
        
        dedup_manager = MemoryDeduplicationManager()
        
        # 测试方法是否存在且可调用
        methods_to_test = [
            'load_table_to_memory',
            'deduplicate_records', 
            'update_memory_cache',
            'process_with_deduplication',
            'get_deduplication_stats',
            'is_table_supported'
        ]
        
        for method_name in methods_to_test:
            if hasattr(dedup_manager, method_name):
                method = getattr(dedup_manager, method_name)
                if callable(method):
                    logger.info(f"✅ 方法 {method_name} 存在且可调用")
                else:
                    logger.error(f"❌ 方法 {method_name} 存在但不可调用")
                    return False
            else:
                logger.error(f"❌ 方法 {method_name} 不存在")
                return False
        
        logger.info("✅ 方法签名兼容性测试通过")
        return True
        
    except Exception as e:
        logger.error(f"方法签名兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始事务修复测试")
    
    test_functions = [
        ("事务兼容性", test_transaction_compatibility),
        ("连接参数传递", test_connection_parameter),
        ("方法签名兼容性", test_method_signatures)
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("事务修复测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 事务修复测试全部通过！")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
