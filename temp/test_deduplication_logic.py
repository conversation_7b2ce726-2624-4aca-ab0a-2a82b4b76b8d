#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存去重逻辑测试 - 独立测试
测试去重算法的核心逻辑，不依赖数据库连接
"""

import json
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Set, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleDeduplicationTester:
    """简化的去重测试器"""
    
    def __init__(self):
        self.target_tables = {
            'algorithm_results': {
                'exclude_fields': ['id', 'created_at', 'updated_at']
            },
            'wash_trading_results': {
                'exclude_fields': ['id', 'created_at']
            },
            'same_account_wash_trading': {
                'exclude_fields': ['id', 'created_at']
            },
            'cross_account_wash_trading': {
                'exclude_fields': ['id', 'created_at']
            }
        }
        self.memory_cache: Dict[str, Set[str]] = {}
    
    def _normalize_record_for_comparison(self, record: Dict[str, Any], table_name: str) -> str:
        """将记录标准化为可比较的哈希值"""
        try:
            table_config = self.target_tables.get(table_name, {})
            exclude_fields = table_config.get('exclude_fields', [])
            
            # 创建用于比较的字段字典
            comparison_dict = {}
            for key, value in record.items():
                if key not in exclude_fields:
                    # 标准化值
                    if value is None:
                        comparison_dict[key] = None
                    elif isinstance(value, (dict, list)):
                        comparison_dict[key] = json.dumps(value, sort_keys=True, ensure_ascii=False)
                    elif isinstance(value, datetime):
                        comparison_dict[key] = value.isoformat()
                    elif isinstance(value, (int, float)):
                        comparison_dict[key] = value
                    else:
                        comparison_dict[key] = str(value)
            
            # 生成哈希值
            record_str = json.dumps(comparison_dict, sort_keys=True, ensure_ascii=False)
            record_hash = hashlib.md5(record_str.encode('utf-8')).hexdigest()
            
            return record_hash
            
        except Exception as e:
            logger.error(f"标准化记录失败 {table_name}: {e}")
            return hashlib.md5(str(record).encode('utf-8')).hexdigest()
    
    def deduplicate_records(self, table_name: str, new_records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对新记录进行全字段匹配去重"""
        if not new_records:
            return []
        
        # 确保缓存存在
        if table_name not in self.memory_cache:
            self.memory_cache[table_name] = set()
        
        unique_records = []
        duplicate_count = 0
        
        for record in new_records:
            # 生成记录哈希
            record_hash = self._normalize_record_for_comparison(record, table_name)
            
            # 检查是否重复
            if record_hash not in self.memory_cache[table_name]:
                unique_records.append(record)
                self.memory_cache[table_name].add(record_hash)
            else:
                duplicate_count += 1
                logger.debug(f"发现重复记录: {record_hash[:8]}...")
        
        logger.info(f"表 {table_name} 去重完成: 原始{len(new_records)}条 → 唯一{len(unique_records)}条 (过滤{duplicate_count}条重复)")
        
        return unique_records

def test_basic_deduplication():
    """测试基础去重功能"""
    logger.info("=== 测试基础去重功能 ===")
    
    tester = SimpleDeduplicationTester()
    
    # 测试数据
    test_records = [
        {
            'id': 1,
            'task_id': 'test_task_1',
            'algorithm_type': 'wash_trading',
            'risk_level': 'medium',
            'confidence_score': 0.8,
            'created_at': datetime.now()
        },
        {
            'id': 2,  # 不同的ID
            'task_id': 'test_task_1',  # 但业务字段相同
            'algorithm_type': 'wash_trading',
            'risk_level': 'medium',
            'confidence_score': 0.8,
            'created_at': datetime.now()  # 不同的时间
        },
        {
            'id': 3,
            'task_id': 'test_task_2',  # 不同的业务数据
            'algorithm_type': 'high_frequency',
            'risk_level': 'high',
            'confidence_score': 0.9,
            'created_at': datetime.now()
        }
    ]
    
    # 执行去重
    unique_records = tester.deduplicate_records('algorithm_results', test_records)
    
    # 验证结果
    expected_count = 2  # 前两条记录业务字段相同，应该只保留一条
    if len(unique_records) == expected_count:
        logger.info("✅ 基础去重功能测试通过")
        return True
    else:
        logger.error(f"❌ 基础去重功能测试失败: 期望{expected_count}条，实际{len(unique_records)}条")
        return False

def test_json_field_deduplication():
    """测试JSON字段去重"""
    logger.info("=== 测试JSON字段去重 ===")
    
    tester = SimpleDeduplicationTester()
    
    # 测试包含JSON字段的记录
    test_records = [
        {
            'id': 1,
            'task_id': 'json_test_1',
            'indicators': {'risk_score': 0.8, 'volume': 1000},
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'task_id': 'json_test_1',
            'indicators': {'volume': 1000, 'risk_score': 0.8},  # 相同内容，不同顺序
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'task_id': 'json_test_1',
            'indicators': {'risk_score': 0.9, 'volume': 1000},  # 不同内容
            'created_at': datetime.now()
        }
    ]
    
    # 执行去重
    unique_records = tester.deduplicate_records('algorithm_results', test_records)
    
    # 验证结果：前两条记录的JSON内容相同（忽略顺序），应该只保留一条
    expected_count = 2
    if len(unique_records) == expected_count:
        logger.info("✅ JSON字段去重测试通过")
        return True
    else:
        logger.error(f"❌ JSON字段去重测试失败: 期望{expected_count}条，实际{len(unique_records)}条")
        return False

def test_batch_deduplication():
    """测试批量去重性能"""
    logger.info("=== 测试批量去重性能 ===")
    
    tester = SimpleDeduplicationTester()
    
    # 生成大量测试数据
    batch_size = 1000
    batch_records = []
    
    for i in range(batch_size):
        record = {
            'id': i,
            'task_id': f'batch_task_{i % 100}',  # 100个不同的task_id，会有重复
            'algorithm_type': 'batch_test',
            'risk_level': 'medium',
            'confidence_score': 0.8,
            'trading_volume': float((i % 100) * 100),  # 修复：使用 i % 100 让数据重复
            'created_at': datetime.now()
        }
        batch_records.append(record)
    
    # 执行批量去重
    start_time = datetime.now()
    unique_records = tester.deduplicate_records('algorithm_results', batch_records)
    end_time = datetime.now()
    
    processing_time = (end_time - start_time).total_seconds()
    
    logger.info(f"批量去重完成:")
    logger.info(f"  - 处理时间: {processing_time:.3f}秒")
    logger.info(f"  - 原始记录: {len(batch_records)}")
    logger.info(f"  - 唯一记录: {len(unique_records)}")
    logger.info(f"  - 重复记录: {len(batch_records) - len(unique_records)}")
    
    # 验证性能和结果
    expected_unique = 100  # 应该有100个唯一的task_id
    performance_ok = processing_time < 1.0  # 应该在1秒内完成
    result_ok = len(unique_records) == expected_unique
    
    if performance_ok and result_ok:
        logger.info("✅ 批量去重性能测试通过")
        return True
    else:
        if not performance_ok:
            logger.error(f"❌ 性能测试失败: 处理时间{processing_time:.3f}秒超过1秒")
        if not result_ok:
            logger.error(f"❌ 结果测试失败: 期望{expected_unique}条唯一记录，实际{len(unique_records)}条")
        return False

def test_multiple_tables():
    """测试多表去重"""
    logger.info("=== 测试多表去重 ===")
    
    tester = SimpleDeduplicationTester()
    
    # 测试不同表的去重
    algorithm_records = [
        {'id': 1, 'task_id': 'multi_test', 'algorithm_type': 'wash_trading', 'created_at': datetime.now()},
        {'id': 2, 'task_id': 'multi_test', 'algorithm_type': 'wash_trading', 'created_at': datetime.now()}
    ]
    
    wash_trading_records = [
        {'id': 1, 'algorithm_result_id': 1, 'trading_type': 'wash_trading', 'created_at': datetime.now()},
        {'id': 2, 'algorithm_result_id': 1, 'trading_type': 'wash_trading', 'created_at': datetime.now()}
    ]
    
    # 分别去重
    unique_algorithm = tester.deduplicate_records('algorithm_results', algorithm_records)
    unique_wash_trading = tester.deduplicate_records('wash_trading_results', wash_trading_records)
    
    # 验证结果
    if len(unique_algorithm) == 1 and len(unique_wash_trading) == 1:
        logger.info("✅ 多表去重测试通过")
        return True
    else:
        logger.error(f"❌ 多表去重测试失败: algorithm_results={len(unique_algorithm)}, wash_trading_results={len(unique_wash_trading)}")
        return False

def main():
    """主测试函数"""
    logger.info("开始内存去重逻辑测试")
    
    test_functions = [
        ("基础去重功能", test_basic_deduplication),
        ("JSON字段去重", test_json_field_deduplication),
        ("批量去重性能", test_batch_deduplication),
        ("多表去重", test_multiple_tables)
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有去重逻辑测试通过！")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
